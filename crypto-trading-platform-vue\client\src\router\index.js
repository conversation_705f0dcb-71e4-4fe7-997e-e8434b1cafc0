import { createRouter, createWebHistory } from 'vue-router'

// 检查用户是否已登录
const isAuthenticated = () => {
  const token = localStorage.getItem('token')
  const user = localStorage.getItem('user')
  return !!(token && user)
}

// 路由守卫
const requireAuth = (to, from, next) => {
  if (isAuthenticated()) {
    next()
  } else {
    next({ name: 'login' })
  }
}

// 已登录用户重定向
const redirectIfAuthenticated = (to, from, next) => {
  if (isAuthenticated()) {
    next({ name: 'home' })
  } else {
    next()
  }
}

// 管理员权限检查
const requireAdmin = (to, from, next) => {
  if (isAuthenticated()) {
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    if (user && user.isAdmin) {
      next()
    } else {
      alert('需要管理员权限')
      next({ name: 'home' })
    }
  } else {
    next({ name: 'login' })
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      beforeEnter: redirectIfAuthenticated
    },
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/market',
      name: 'market',
      component: () => import('../views/MarketView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/okx',
      name: 'okx',
      component: () => import('../views/OkxView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/binance',
      name: 'binance',
      component: () => import('../views/BinanceView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/market-exchange',
      name: 'market-exchange',
      component: () => import('../views/MarketExchangeView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/user',
      name: 'user',
      component: () => import('../views/UserView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/exchange-register',
      name: 'exchange-register',
      component: () => import('../views/ExchangeRegisterView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/api-intro',
      name: 'api-intro',
      component: () => import('../views/ApiIntroView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/strategy-intro',
      name: 'strategy-intro',
      component: () => import('../views/StrategyIntroView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/beginner-guide',
      name: 'beginner-guide',
      component: () => import('../views/BeginnerGuideView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/invite-friends',
      name: 'invite-friends',
      component: () => import('../views/InviteFriendsView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/my-earnings',
      name: 'my-earnings',
      component: () => import('../views/MyEarningsView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/ranking',
      name: 'ranking',
      component: () => import('../views/RankingView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/ranking-management',
      name: 'ranking-management',
      component: () => import('../views/RankingManagementView.vue'),
      beforeEnter: requireAdmin
    },
    {
      path: '/contact-service',
      name: 'contact-service',
      component: () => import('../views/ContactServiceView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/change-password',
      name: 'change-password',
      component: () => import('../views/ChangePasswordView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/membership',
      name: 'membership',
      component: () => import('../views/MembershipView.vue'),
      beforeEnter: requireAuth
    },
    {
      path: '/membership-management',
      name: 'membership-management',
      component: () => import('../views/MembershipManagementView.vue'),
      beforeEnter: requireAdmin
    },
    {
      path: '/user-management',
      name: 'user-management',
      component: () => import('../views/UserManagementView.vue'),
      beforeEnter: requireAdmin
    }
  ]
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 如果没有匹配的路由，重定向到登录页面
  if (to.matched.length === 0) {
    if (isAuthenticated()) {
      next({ name: 'home' })
    } else {
      next({ name: 'login' })
    }
  } else {
    next()
  }
})

export default router
